<?php
require_once('phpqrcode/qrlib.php');
require_once('db.php');
require_once('network_security.php');

date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

session_start();

// Admin authentication - check session
$is_admin = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$admin_name = $_SESSION['admin_name'] ?? '';

// Redirect non-admins to login page
if (!$is_admin) {
    header("Location: admin_login.php");
    exit();
}

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

// Generate device token
function generateDeviceToken() {
    return bin2hex(random_bytes(16));
}

$message = '';
$qr_generated = false;
$qr_file = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'generate_registration') {
        // Generate registration QR code - permanent, no expiry
        $qr_data = "https://172.31.18.254/attendance/register_phone.php";

        // Create qrcodes directory if it doesn't exist
        if (!file_exists('qrcodes')) {
            mkdir('qrcodes', 0777, true);
        }

        $qr_file = 'qrcodes/registration_qr.png';
        QRcode::png($qr_data, $qr_file, QR_ECLEVEL_H, 10);

        debug_log("Generated registration QR code");
        $message = "✅ Registration QR code generated successfully!";
        $qr_generated = true;

    } elseif ($action === 'generate_attendance') {
        // Generate attendance QR code - permanent, no expiry, works for all registered users
        $qr_data = "https://172.31.18.254/attendance/attendance.php";

        // Create qrcodes directory if it doesn't exist
        if (!file_exists('qrcodes')) {
            mkdir('qrcodes', 0777, true);
        }

        $qr_file = 'qrcodes/attendance_qr.png';
        QRcode::png($qr_data, $qr_file, QR_ECLEVEL_H, 10);

        debug_log("Generated attendance QR code");
        $message = "✅ Attendance QR code generated successfully!";
        $qr_generated = true;
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>QR Code Generator</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        .header-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .header-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .content {
            padding: 30px 20px;
        }
        .instructions {
            background: linear-gradient(135deg, #e7f3ff 0%, #bbdefb 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }
        .instructions h4 {
            margin-top: 0;
            color: #1976d2;
            font-size: 1.1rem;
        }
        .instructions ul {
            margin: 15px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
            color: #333;
        }
        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            transition: all 0.3s ease;
        }
        .form-section:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }
        .form-section h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.3rem;
            margin-bottom: 15px;
        }
        .form-section p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        button {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .registration-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }
        .attendance-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .message {
            padding: 20px;
            margin: 25px 0;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
        }
        .success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .qr-display {
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 2px solid #dee2e6;
        }
        .qr-display h3 {
            color: #333;
            margin-bottom: 20px;
        }
        .qr-display img {
            max-width: 300px;
            width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .qr-display p {
            margin: 15px 0;
            color: #666;
        }
        .nav-links {
            text-align: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #e9ecef;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
            .header {
                padding: 20px 15px;
            }
            .header h1 {
                font-size: 1.5rem;
            }
            .header-btn {
                position: static;
                display: block;
                text-align: center;
                margin: 20px auto 0 auto;
                max-width: 120px;
            }
            .content {
                padding: 20px 15px;
            }
            .form-section {
                padding: 20px 15px;
            }
            .nav-links a {
                display: block;
                margin: 10px 0;
            }
            .qr-display {
                padding: 20px 15px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.3rem;
            }
            button {
                padding: 12px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="index.php" class="header-btn">🏠 Home</a>
            <h1>🔗 QR Code Generator</h1>
            <p>Admin Access - Welcome, <?= htmlspecialchars($admin_name) ?>!</p>
        </div>

        <div class="content">
            <div class="instructions">
                <h4>📋 Enhanced 2-QR Security System:</h4>
                <ul>
                    <li><strong>Registration QR Code:</strong> Users scan this to register their device with enhanced security fingerprinting + get unique ID</li>
                    <li><strong>Attendance QR Code:</strong> All users scan this to clock in/out (system identifies them automatically using device fingerprinting)</li>
                    <li>QR codes are permanent - no expiry needed</li>
                    <li>Enhanced security: Each device is uniquely identified using multiple characteristics</li>
                    <li>Each user can only clock in/out for themselves based on their registered device</li>
                </ul>
            </div>

            <?php if ($message): ?>
                <div class="message success">
                    <?= $message ?>
                </div>
            <?php endif; ?>

            <div class="form-section">
                <h3>📱 Generate Registration QR Code</h3>
                <p><strong>Purpose:</strong> Users scan this QR code to register their device with enhanced security fingerprinting and get a unique ID.</p>
                <p><strong>Usage:</strong> Print this QR code and place it where new users can scan it to register their devices securely.</p>
                <form method="POST">
                    <input type="hidden" name="action" value="generate_registration">
                    <button type="submit" class="registration-btn">📱 Generate Registration QR Code</button>
                </form>

                <?php if ($qr_generated && $qr_file && file_exists($qr_file) && strpos($qr_file, 'registration') !== false): ?>
                    <div class="qr-display" style="margin-top: 25px;">
                        <h4>📱 Registration QR Code Generated!</h4>
                        <img src="<?= htmlspecialchars($qr_file) ?>" alt="Registration QR Code">
                        <p><strong>📁 File:</strong> <?= htmlspecialchars($qr_file) ?></p>
                        <p><strong>⏰ Status:</strong> <span style="color: #28a745; font-weight: 600;">Permanent (No expiry)</span></p>
                        <p style="background: rgba(102, 126, 234, 0.1); padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <em>💡 Right-click the image to save, print, or share</em>
                        </p>
                        <p><strong>📋 Usage:</strong> Place this QR code where new users can scan it to register their devices with enhanced security and get unique IDs.</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="form-section">
                <h3>📋 Generate Attendance QR Code</h3>
                <p><strong>Purpose:</strong> All registered users scan this QR code to clock in/out.</p>
                <p><strong>Usage:</strong> Print this QR code and place it at the attendance location. System automatically identifies users by their device fingerprint.</p>
                <form method="POST">
                    <input type="hidden" name="action" value="generate_attendance">
                    <button type="submit" class="attendance-btn">📋 Generate Attendance QR Code</button>
                </form>

                <?php if ($qr_generated && $qr_file && file_exists($qr_file) && strpos($qr_file, 'attendance') !== false): ?>
                    <div class="qr-display" style="margin-top: 25px;">
                        <h4>📋 Attendance QR Code Generated!</h4>
                        <img src="<?= htmlspecialchars($qr_file) ?>" alt="Attendance QR Code">
                        <p><strong>📁 File:</strong> <?= htmlspecialchars($qr_file) ?></p>
                        <p><strong>⏰ Status:</strong> <span style="color: #28a745; font-weight: 600;">Permanent (No expiry)</span></p>
                        <p style="background: rgba(102, 126, 234, 0.1); padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <em>💡 Right-click the image to save, print, or share</em>
                        </p>
                        <p><strong>📋 Usage:</strong> Place this QR code at the attendance location for daily clock in/out. System will automatically identify users by their device fingerprint.</p>
                    </div>
                <?php endif; ?>
            </div>


        </div>
    </div>
</body>
</html>
