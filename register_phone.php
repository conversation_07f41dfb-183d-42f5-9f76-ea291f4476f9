<?php
session_start();
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

// Generate unique ID function
function generateUniqueID() {
    return 'UID-' . strtoupper(bin2hex(random_bytes(4))) . '-' . date('Ymd');
}

// Generate device fingerprint from device info
function generateDeviceFingerprint($deviceInfo) {
    // Create a consistent hash from device characteristics
    $fingerprintData = [
        'userAgent' => $deviceInfo['userAgent'] ?? '',
        'screenResolution' => $deviceInfo['screenResolution'] ?? '',
        'timezone' => $deviceInfo['timezone'] ?? '',
        'language' => $deviceInfo['language'] ?? '',
        'platform' => $deviceInfo['platform'] ?? '',
        'hardwareConcurrency' => $deviceInfo['hardwareConcurrency'] ?? '',
        'colorDepth' => $deviceInfo['colorDepth'] ?? '',
        'touchSupport' => $deviceInfo['touchSupport'] ?? ''
    ];

    // Sort to ensure consistent ordering
    ksort($fingerprintData);
    $fingerprintString = json_encode($fingerprintData);

    return hash('sha256', $fingerprintString);
}

// Check if user is admin (based on session)
$is_admin = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

$message = '';
$registration_status = '';
$phone_model = '';
$device_fingerprint = '';

// Get device data from POST
$phone_model = $_POST['phone_model'] ?? ($_COOKIE['phone_model'] ?? null);
$device_info_json = $_POST['device_info'] ?? null;

// Generate device fingerprint if we have device info
if ($device_info_json) {
    $device_info = json_decode($device_info_json, true);
    if ($device_info) {
        $device_fingerprint = generateDeviceFingerprint($device_info);
        debug_log("Generated device fingerprint: " . $device_fingerprint);
    }
}

// Check if device is already registered using fingerprint (primary) or phone model (fallback)
if ($device_fingerprint) {
    // Check by device fingerprint first (most accurate)
    $stmt = $conn->prepare("SELECT full_name, unique_id, registration_timestamp, phone_model FROM staff WHERE device_fingerprint = ? AND unique_id IS NOT NULL AND unique_id != ''");
    $stmt->bind_param("s", $device_fingerprint);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $existing_user = $result->fetch_assoc();
        $registration_status = 'registered';
        $message = "✅ This device is already registered!<br>" .
                  "<strong>User:</strong> " . htmlspecialchars($existing_user['full_name']) . "<br>" .
                  "<strong>Unique ID:</strong> " . htmlspecialchars($existing_user['unique_id']) . "<br>" .
                  "<strong>Registered:</strong> " . date('M j, Y', strtotime($existing_user['registration_timestamp'] ?? $existing_user['created_at'])) . "<br>" .
                  "<em>You can now scan the Attendance QR Code to clock in/out.</em>";
    } else {
        $registration_status = 'not_registered';
    }
} else {
    // No device fingerprint available - allow registration to proceed
    $registration_status = 'not_registered';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? 'register';

    if ($action === 'check_status') {
        // Just checking status, don't process registration
        $phone_model = $_POST['phone_model'] ?? '';
        // The status check logic above will handle this
    }

    if ($action === 'register') {
        $staff_id = intval($_POST['staff_id']);
        $phone_model_post = $_POST['phone_model'] ?? '';

        if ($staff_id && $phone_model_post) {
            // Check if staff exists and get current data
            $stmt = $conn->prepare("SELECT full_name, phone_model, unique_id FROM staff WHERE id = ?");
            $stmt->bind_param("i", $staff_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                // SECURITY: Check if user is already registered
                if (!empty($row['phone_model']) && !empty($row['unique_id'])) {
                    debug_log("Registration attempt blocked - " . $row['full_name'] . " already registered with phone: " . $row['phone_model']);
                    $message = "⚠️ " . htmlspecialchars($row['full_name']) . " is already registered!<br>" .
                              "Registered Phone: <strong>" . htmlspecialchars($row['phone_model']) . "</strong><br>" .
                              "Unique ID: <strong>" . htmlspecialchars($row['unique_id']) . "</strong><br>" .
                              "<em>Each user can only register once with one specific phone.</em>";
                    $registration_status = 'registered';
                } else {
                    // Get device info for enhanced fingerprinting
                    $device_info_json = $_POST['device_info'] ?? '';

                    if (empty($device_info_json)) {
                        $message = "❌ Device information could not be processed. Please refresh and try again.";
                        $registration_status = 'error';
                    } else {
                        $device_info = json_decode($device_info_json, true);
                        if (!$device_info) {
                            $message = "❌ Device information is invalid. Please refresh and try again.";
                            $registration_status = 'error';
                        } else {
                            $device_fingerprint = generateDeviceFingerprint($device_info);

                            // Check if this device fingerprint is already registered by another user
                            $fingerprintCheckStmt = $conn->prepare("SELECT full_name, unique_id FROM staff WHERE device_fingerprint = ? AND id != ? AND unique_id IS NOT NULL AND unique_id != ''");
                            $fingerprintCheckStmt->bind_param("si", $device_fingerprint, $staff_id);
                            $fingerprintCheckStmt->execute();
                            $fingerprintResult = $fingerprintCheckStmt->get_result();

                            if ($fingerprintResult->num_rows > 0) {
                                $existingUser = $fingerprintResult->fetch_assoc();
                                debug_log("Registration attempt blocked - Device fingerprint already registered by: " . $existingUser['full_name']);
                                $message = "❌ This device is already registered by <strong>" . htmlspecialchars($existingUser['full_name']) . "</strong> (ID: " . htmlspecialchars($existingUser['unique_id']) . ")!<br>" .
                                          "<em>Each device can only be registered to one user.</em>";
                                $registration_status = 'error';
                            } else {
                                // Generate unique ID and register with enhanced device identification
                                $unique_id = generateUniqueID();
                                $device_token = bin2hex(random_bytes(16));
                                $registration_timestamp = date('Y-m-d H:i:s');

                                // Update with enhanced device identification
                                $updateStmt = $conn->prepare("UPDATE staff SET phone_model = ?, unique_id = ?, device_token = ?, device_fingerprint = ?, device_info = ?, registration_timestamp = ? WHERE id = ?");
                                $updateStmt->bind_param("ssssssi", $phone_model_post, $unique_id, $device_token, $device_fingerprint, $device_info_json, $registration_timestamp, $staff_id);

                                if ($updateStmt->execute()) {
                                    // Set cookies for future reference
                                    setcookie('phone_model', $phone_model_post, time() + 86400, '/');
                                    setcookie('device_fingerprint', $device_fingerprint, time() + 86400, '/');

                                    debug_log("Enhanced device registration for " . $row['full_name'] . " - Fingerprint: " . $device_fingerprint . " - Unique ID: " . $unique_id);
                                    $message = "✅ Device registered successfully with enhanced security!<br>" .
                                              "<strong>Name:</strong> " . htmlspecialchars($row['full_name']) . "<br>" .
                                              "<strong>Unique ID:</strong> " . htmlspecialchars($unique_id) . "<br>" .
                                              "<strong>Device:</strong> " . htmlspecialchars($phone_model_post) . "<br>" .
                                              "<strong>Registered:</strong> " . date('M j, Y H:i', strtotime($registration_timestamp)) . "<br>" .
                                              "<em>You can now scan the Attendance QR Code to clock in/out.</em>";
                                    $registration_status = 'success';
                                } else {
                                    $message = "❌ Error registering device. Please try again.";
                                    $registration_status = 'error';
                                    debug_log("Registration failed for " . $row['full_name'] . ": " . $conn->error);
                                }
                                $updateStmt->close();
                            }
                            $fingerprintCheckStmt->close();
                        }
                    }
                }
            } else {
                $message = "❌ Staff member not found.";
                $registration_status = 'error';
            }
            $stmt->close();
        } else {
            $message = "❌ Please select a staff member and ensure phone detection is working.";
            $registration_status = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Register Phone Model</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }
        .content {
            padding: 30px 20px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
        }
        select, button {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            margin-top: 10px;
            transition: transform 0.2s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        button:active {
            transform: translateY(0);
        }
        .message {
            padding: 20px;
            margin: 25px 0;
            border-radius: 10px;
            font-weight: 500;
            line-height: 1.5;
        }
        .success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        .warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        .phone-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 4px solid #2196f3;
        }
        .phone-info strong {
            color: #1976d2;
        }
        .registered-phones {
            margin-top: 40px;
        }
        .registered-phones h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            min-width: 500px;
        }
        th, td {
            padding: 15px 10px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
            font-size: 0.9rem;
        }
        th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
            color: #495057;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .nav-links {
            text-align: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e1e5e9;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        .qr-section {
            margin: 40px 0;
            padding: 30px 0;
            text-align: center;
            border-top: 2px solid #e1e5e9;
        }
        .qr-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .qr-section button {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            max-width: 300px;
        }
        .qr-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            text-align: center;
            border: 2px solid #dee2e6;
        }
        .qr-display img {
            max-width: 250px;
            width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
            .header {
                padding: 20px 15px;
            }
            .header h1 {
                font-size: 1.5rem;
            }
            .content {
                padding: 20px 15px;
            }
            .nav-links a {
                display: block;
                margin: 10px 0;
            }
            th, td {
                padding: 10px 8px;
                font-size: 0.8rem;
            }
            .qr-display {
                padding: 20px 15px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.3rem;
            }
            select, button {
                padding: 12px;
                font-size: 14px;
            }
            .message {
                padding: 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Phone Registration</h1>
            <p>Register your phone to access the attendance system</p>
        </div>

        <div class="content">
            <?php if ($message): ?>
                <div class="message <?= strpos($message, '✅') !== false ? 'success' : (strpos($message, '⚠️') !== false ? 'warning' : 'error') ?>">
                    <?= $message ?>
                </div>
            <?php endif; ?>


            <?php if ($registration_status === 'registered'): ?>
                <!-- Already Registered - Show Status Only -->
                <div style="text-align: center; margin: 30px 0; padding: 20px; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border-radius: 15px; border-left: 6px solid #28a745;">
                    <h3 style="margin-top: 0; color: #155724;">✅ Device Already Registered</h3>
                    <p style="color: #155724; margin-bottom: 20px;">This device is already registered in the system.</p>
                    <p style="color: #666; font-size: 0.9rem;"><em>You can now scan the Attendance QR Code to clock in/out for your daily work.</em></p>
                </div>
            <?php elseif ($registration_status === 'success'): ?>
                <!-- Registration Successful -->
                <div style="text-align: center; margin: 30px 0; padding: 20px; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border-radius: 15px; border-left: 6px solid #28a745;">
                    <h3 style="margin-top: 0; color: #155724;">🎉 Registration Complete!</h3>
                    <p style="color: #155724; margin-bottom: 20px;">Your device has been successfully registered.</p>
                    <p style="color: #666; font-size: 0.9rem;"><em>You can now scan the Attendance QR Code to clock in/out for your daily work.</em></p>
                </div>
            <?php else: ?>
                <!-- Show Registration Form -->
                <form method="POST">
                    <div class="form-group">
                        <label for="staff_id">👤 Select Your Name:</label>
                        <select name="staff_id" id="staff_id" required>
                            <option value="">-- Choose Your Name --</option>
                            <?php
                            $result = $conn->query("SELECT id, full_name FROM staff ORDER BY full_name ASC");
                            while ($row = $result->fetch_assoc()) {
                                echo "<option value='{$row['id']}'>" . htmlspecialchars($row['full_name']) . "</option>";
                            }
                            ?>
                        </select>
                    </div>

                    <input type="hidden" name="phone_model" id="phone_model" value="">
                    <input type="hidden" name="device_info" id="device_info" value="">
                    <input type="hidden" name="action" value="register">
                    <button type="submit">🔐 Register This Device</button>
                </form>
            <?php endif; ?>







        </div>
    </div>

    <script>
    // Enhanced device fingerprinting and phone model detection
    (function() {
        // Collect comprehensive device information
        function collectDeviceInfo() {
            const ua = navigator.userAgent;

            // Basic phone model detection
            let phoneModel;
            if (/iPhone|iPad|iPod/.test(ua)) {
                const versionMatch = ua.match(/OS (\d+_\d+)/);
                const version = versionMatch ? versionMatch[1].replace("_", ".") : "unknown";
                phoneModel = "iPhone iOS " + version;
            } else if (/Android/.test(ua)) {
                const androidMatch = ua.match(/Android ([0-9\.]+)/);
                const version = androidMatch ? androidMatch[1] : "unknown";
                phoneModel = "Android " + version;
            } else {
                const match = ua.match(/\(([^)]+)\)/);
                if (match) {
                    const parts = match[1].split(";");
                    phoneModel = parts.slice(1).join(";").trim();
                } else {
                    phoneModel = "UnknownPhone";
                }
            }
            phoneModel += ` (${screen.width}x${screen.height})`;

            // Comprehensive device information for fingerprinting
            const deviceInfo = {
                userAgent: ua,
                screenResolution: `${screen.width}x${screen.height}`,
                colorDepth: screen.colorDepth || 'unknown',
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown',
                language: navigator.language || navigator.userLanguage || 'unknown',
                platform: navigator.platform || 'unknown',
                hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
                deviceMemory: navigator.deviceMemory || 'unknown',
                touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0 ? 'yes' : 'no',
                cookieEnabled: navigator.cookieEnabled ? 'yes' : 'no',
                doNotTrack: navigator.doNotTrack || 'unknown',
                onLine: navigator.onLine ? 'yes' : 'no'
            };

            return { phoneModel, deviceInfo };
        }

        const { phoneModel, deviceInfo } = collectDeviceInfo();

        // Set form values
        const phoneModelField = document.getElementById('phone_model');
        const deviceInfoField = document.getElementById('device_info');

        if (phoneModelField) {
            phoneModelField.value = phoneModel;
        }

        if (deviceInfoField) {
            deviceInfoField.value = JSON.stringify(deviceInfo);
        }

        // Auto-submit to check registration status if not already checked
        if (!<?= json_encode($phone_model !== null) ?>) {
            // Create form to submit device information for registration check
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '';

            const pmInput = document.createElement('input');
            pmInput.type = 'hidden';
            pmInput.name = 'phone_model';
            pmInput.value = phoneModel;
            form.appendChild(pmInput);

            const deviceInfoInput = document.createElement('input');
            deviceInfoInput.type = 'hidden';
            deviceInfoInput.name = 'device_info';
            deviceInfoInput.value = JSON.stringify(deviceInfo);
            form.appendChild(deviceInfoInput);

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'check_status';
            form.appendChild(actionInput);

            document.body.appendChild(form);
            form.submit();
        }

    })();
    </script>
</body>
</html>






