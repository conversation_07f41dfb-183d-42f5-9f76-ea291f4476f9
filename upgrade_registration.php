<?php
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $unique_id = trim($_POST['unique_id'] ?? '');
    $device_info_json = $_POST['device_info'] ?? '';
    
    if (empty($unique_id)) {
        $error = "Please enter your Unique ID.";
    } else {
        // Check if user exists and is legacy (no device fingerprint)
        $stmt = $conn->prepare("SELECT id, full_name, eid_cid_permit, phone_model, device_fingerprint FROM staff WHERE unique_id = ?");
        $stmt->bind_param("s", $unique_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            if (!empty($row['device_fingerprint'])) {
                $error = "Your account is already upgraded to enhanced security. No re-registration needed.";
            } else {
                // Parse device info
                $device_info = json_decode($device_info_json, true);
                
                if ($device_info) {
                    // Generate device fingerprint
                    function generateDeviceFingerprint($deviceInfo) {
                        $fingerprintData = [
                            'userAgent' => $deviceInfo['userAgent'] ?? '',
                            'screenResolution' => $deviceInfo['screenResolution'] ?? '',
                            'timezone' => $deviceInfo['timezone'] ?? '',
                            'language' => $deviceInfo['language'] ?? '',
                            'platform' => $deviceInfo['platform'] ?? '',
                            'hardwareConcurrency' => $deviceInfo['hardwareConcurrency'] ?? '',
                            'colorDepth' => $deviceInfo['colorDepth'] ?? '',
                            'touchSupport' => $deviceInfo['touchSupport'] ?? ''
                        ];
                        ksort($fingerprintData);
                        return hash('sha256', json_encode($fingerprintData));
                    }
                    
                    $device_fingerprint = generateDeviceFingerprint($device_info);
                    $device_token = bin2hex(random_bytes(16));
                    $registration_timestamp = date('Y-m-d H:i:s');
                    
                    // Update user with enhanced security data (preserve all existing data)
                    $update_stmt = $conn->prepare("UPDATE staff SET device_fingerprint = ?, device_info = ?, device_token = ?, registration_timestamp = ? WHERE id = ?");
                    $update_stmt->bind_param("ssssi", $device_fingerprint, $device_info_json, $device_token, $registration_timestamp, $row['id']);
                    
                    if ($update_stmt->execute()) {
                        $message = "✅ Successfully upgraded " . htmlspecialchars($row['full_name']) . " to enhanced security! Your attendance history and current status remain unchanged.";
                    } else {
                        $error = "Failed to upgrade registration. Please try again.";
                    }
                    $update_stmt->close();
                } else {
                    $error = "Failed to collect device information. Please try again.";
                }
            }
        } else {
            $error = "Unique ID not found. Please check your ID or contact administrator.";
        }
        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upgrade Registration - Enhanced Security</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: left;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .info-box ul {
            color: #333;
            margin-left: 20px;
        }
        
        .info-box li {
            margin-bottom: 5px;
        }
        
        .back-link {
            margin-top: 20px;
        }
        
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        #device-status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .status-collecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-ready {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Upgrade Registration</h1>
            <p>Upgrade to Enhanced Security System</p>
        </div>
        
        <?php if ($message): ?>
            <div class="message success"><?= $message ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="message error"><?= $error ?></div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📋 What This Does:</h3>
            <ul>
                <li>Upgrades your registration to enhanced security</li>
                <li>Preserves all your attendance history</li>
                <li>Maintains your current clock in/out status</li>
                <li>Adds device fingerprinting for better security</li>
                <li>No impact on existing attendance data</li>
            </ul>
        </div>
        
        <form method="POST" id="upgradeForm">
            <div class="form-group">
                <label for="unique_id">🔐 Your Unique ID:</label>
                <input type="text" id="unique_id" name="unique_id" placeholder="Enter your Unique ID (e.g., UID-ABC123-20250620)" required>
            </div>
            
            <div id="device-status" class="status-collecting">
                🔄 Collecting device information...
            </div>
            
            <input type="hidden" id="device_info" name="device_info">
            
            <button type="submit" class="btn" id="submitBtn" disabled>
                🔒 Upgrade to Enhanced Security
            </button>
        </form>
        
        <div class="back-link">
            <a href="index.php">← Back to Main Page</a>
        </div>
    </div>

    <script>
        // Collect comprehensive device information
        function collectDeviceInfo() {
            const deviceInfo = {
                userAgent: navigator.userAgent,
                screenResolution: `${screen.width}x${screen.height}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                language: navigator.language,
                platform: navigator.platform,
                hardwareConcurrency: navigator.hardwareConcurrency || 0,
                colorDepth: screen.colorDepth,
                touchSupport: 'ontouchstart' in window,
                deviceMemory: navigator.deviceMemory || 0,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack,
                onlineStatus: navigator.onLine
            };
            
            return deviceInfo;
        }
        
        // Initialize device collection
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const deviceInfo = collectDeviceInfo();
                document.getElementById('device_info').value = JSON.stringify(deviceInfo);
                
                const statusDiv = document.getElementById('device-status');
                const submitBtn = document.getElementById('submitBtn');
                
                statusDiv.className = 'status-ready';
                statusDiv.innerHTML = '✅ Device information collected successfully';
                submitBtn.disabled = false;
            }, 1000);
        });
    </script>
</body>
</html>
