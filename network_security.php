<?php
/**
 * Network Security Module
 * Restricts access to specific IP ranges for the attendance system
 */

function checkNetworkAccess() {
    // Get client IP address
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // Handle forwarded IPs (if behind proxy/load balancer)
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $forwarded_ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $client_ip = trim($forwarded_ips[0]);
    } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
        $client_ip = $_SERVER['HTTP_X_REAL_IP'];
    }
    
    // Allowed network ranges
    $allowed_networks = [
        '***********/24',  // *********** - *************
        '***********/24',  // *********** - *************
        '***********/24'   // *********** - *************
    ];
    
    // Check if client IP is in any allowed network
    foreach ($allowed_networks as $network) {
        if (isIPInNetwork($client_ip, $network)) {
            return true;
        }
    }
    
    // Log unauthorized access attempt
    error_log("SECURITY: Unauthorized network access attempt from IP: " . $client_ip . " at " . date('Y-m-d H:i:s'));
    
    return false;
}

function isIPInNetwork($ip, $network) {
    // Convert network to IP and subnet mask
    list($network_ip, $subnet_bits) = explode('/', $network);
    
    // Convert IPs to long integers
    $ip_long = ip2long($ip);
    $network_long = ip2long($network_ip);
    
    // Calculate subnet mask
    $subnet_mask = -1 << (32 - $subnet_bits);
    
    // Check if IP is in network
    return ($ip_long & $subnet_mask) === ($network_long & $subnet_mask);
}

function blockUnauthorizedAccess() {
    if (!checkNetworkAccess()) {
        // Send 403 Forbidden status
        http_response_code(403);
        
        // Display access denied page
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Access Denied - Attendance System</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                }
                
                .container {
                    background: white;
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
                    padding: 40px;
                    max-width: 500px;
                    width: 100%;
                    text-align: center;
                }
                
                .icon {
                    font-size: 4rem;
                    color: #dc3545;
                    margin-bottom: 20px;
                }
                
                h1 {
                    color: #dc3545;
                    margin-bottom: 20px;
                    font-size: 2rem;
                }
                
                p {
                    color: #666;
                    margin-bottom: 15px;
                    line-height: 1.6;
                }
                
                .ip-info {
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 10px;
                    padding: 15px;
                    margin: 20px 0;
                    font-family: monospace;
                    color: #495057;
                }
                
                .allowed-networks {
                    background: #e3f2fd;
                    border: 1px solid #bbdefb;
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                    text-align: left;
                }
                
                .allowed-networks h3 {
                    color: #1976d2;
                    margin-bottom: 10px;
                }
                
                .allowed-networks ul {
                    margin-left: 20px;
                }
                
                .allowed-networks li {
                    margin-bottom: 5px;
                    font-family: monospace;
                    color: #333;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="icon">🚫</div>
                <h1>Access Denied</h1>
                <p><strong>You are not authorized to access this attendance system.</strong></p>
                <p>This system is restricted to specific network ranges for security purposes.</p>
                
                <div class="ip-info">
                    <strong>Your IP Address:</strong> <?= htmlspecialchars($_SERVER['REMOTE_ADDR'] ?? 'Unknown') ?>
                </div>
                
                <div class="allowed-networks">
                    <h3>🔒 Authorized Networks:</h3>
                    <ul>
                        <li>***********/24 (*********** - *************)</li>
                        <li>***********/24 (*********** - *************)</li>
                        <li>***********/24 (*********** - *************)</li>
                    </ul>
                </div>
                
                <p><strong>If you believe this is an error:</strong></p>
                <p>Please contact your system administrator or ensure you are connected to the authorized network.</p>
            </div>
        </body>
        </html>
        <?php
        exit;
    }
}

// Debug function to show current IP (for testing)
function showCurrentIP() {
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $forwarded_ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $client_ip = trim($forwarded_ips[0]);
    } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
        $client_ip = $_SERVER['HTTP_X_REAL_IP'];
    }
    return $client_ip;
}
?>
