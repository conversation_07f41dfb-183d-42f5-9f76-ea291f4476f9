<?php
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

session_start();

// Admin authentication - check session
$is_admin = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$admin_name = $_SESSION['admin_name'] ?? '';

// Redirect non-admins to login page
if (!$is_admin) {
    header("Location: admin_login.php");
    exit();
}

// Handle user management actions
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'remove_user') {
        $user_id = intval($_POST['user_id']);

        // Get user info before deletion
        $stmt = $conn->prepare("SELECT full_name FROM staff WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $user_result = $stmt->get_result();

        if ($user_result->num_rows > 0) {
            $user_info = $user_result->fetch_assoc();

            // Remove user registration (clear all device identification data)
            $stmt = $conn->prepare("UPDATE staff SET phone_model = NULL, unique_id = NULL, device_token = NULL, device_fingerprint = NULL, device_info = NULL, registration_timestamp = NULL WHERE id = ?");
            $stmt->bind_param("i", $user_id);

            if ($stmt->execute()) {
                $message = "✅ Successfully removed registration for " . htmlspecialchars($user_info['full_name']);
            } else {
                $message = "❌ Error removing user registration.";
            }
        } else {
            $message = "❌ User not found.";
        }
    }
}

// Get attendance data
$attendance_query = "
    SELECT
        s.full_name,
        s.eid_cid_permit,
        s.unique_id,
        s.position_title,
        s.division,
        a.clock_in,
        a.clock_out,
        a.date
    FROM attendance a
    JOIN staff s ON a.staff_id = s.id
    ORDER BY a.date DESC, a.clock_in DESC
";
$attendance_result = $conn->query($attendance_query);

// Get registered users (enhanced with device identification)
$users_query = "SELECT id, full_name, eid_cid_permit, unique_id, phone_model, device_fingerprint, device_info, registration_timestamp, created_at
                FROM staff
                WHERE (phone_model IS NOT NULL AND phone_model != '') OR (device_fingerprint IS NOT NULL AND device_fingerprint != '')
                ORDER BY registration_timestamp DESC, created_at DESC";
$users_result = $conn->query($users_query);

// Get system statistics (enhanced device tracking)
$total_staff = $conn->query("SELECT COUNT(*) as count FROM staff")->fetch_assoc()['count'];
$registered_devices = $conn->query("SELECT COUNT(*) as count FROM staff WHERE (phone_model IS NOT NULL AND phone_model != '') OR (device_fingerprint IS NOT NULL AND device_fingerprint != '')")->fetch_assoc()['count'];
$enhanced_registrations = $conn->query("SELECT COUNT(*) as count FROM staff WHERE device_fingerprint IS NOT NULL AND device_fingerprint != ''")->fetch_assoc()['count'];
$legacy_registrations = $conn->query("SELECT COUNT(*) as count FROM staff WHERE (phone_model IS NOT NULL AND phone_model != '') AND (device_fingerprint IS NULL OR device_fingerprint = '')")->fetch_assoc()['count'];
$today_attendance = $conn->query("SELECT COUNT(*) as count FROM attendance WHERE date = CURDATE()")->fetch_assoc()['count'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Attendance System</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css">
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .header-buttons {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }
        .header-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .header-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        @media (max-width: 768px) {
            .header-buttons {
                position: static;
                justify-content: center;
                margin-top: 20px;
            }
        }
        .content {
            padding: 30px 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .section {
            margin: 40px 0;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 20px;
        }
        .message {
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
        }
        .success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        .user-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .user-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .user-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }
        .user-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .user-info {
            font-size: 0.9rem;
            color: #666;
            margin: 5px 0;
        }
        .user-uid {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1976d2;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            font-weight: 600;
            display: inline-block;
            margin: 10px 0;
        }
        .remove-btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .remove-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        .nav-links {
            text-align: center;
            margin: 30px 0;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        /* DataTables Customization */
        table.dataTable {
            border-collapse: collapse !important;
            width: 100% !important;
        }
        table.dataTable thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }
        table.dataTable tbody tr:nth-child(odd) {
            background-color: #f9f9f9 !important;
        }
        table.dataTable tbody tr:hover {
            background-color: #f1f1f1 !important;
        }
        .dt-buttons {
            margin-bottom: 15px !important;
        }
        .dt-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 5px !important;
            padding: 8px 15px !important;
            margin-right: 10px !important;
            font-weight: 500 !important;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
            .header {
                padding: 20px 15px;
            }
            .header h1 {
                font-size: 1.5rem;
            }
            .content {
                padding: 20px 15px;
            }
            .section {
                padding: 20px 15px;
            }
            .user-grid {
                grid-template-columns: 1fr;
            }
            .nav-links a {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-buttons">
                <a href="index.php" class="header-btn">🏠 Home</a>
                <a href="admin_login.php?logout=1" class="header-btn">🚪 Logout</a>
            </div>
            <h1>👨‍💼 Admin Dashboard</h1>
            <p>Welcome, <?= htmlspecialchars($admin_name) ?> | System Administration</p>
        </div>

        <div class="content">
            <?php if ($message): ?>
                <div class="message <?= strpos($message, '✅') !== false ? 'success' : 'error' ?>">
                    <?= $message ?>
                </div>
            <?php endif; ?>

            <!-- System Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?= $total_staff ?></div>
                    <div class="stat-label">Total Staff</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $registered_devices ?></div>
                    <div class="stat-label">Registered Devices</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $enhanced_registrations ?></div>
                    <div class="stat-label">Enhanced Security</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $legacy_registrations ?></div>
                    <div class="stat-label">Legacy Registrations</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $today_attendance ?></div>
                    <div class="stat-label">Today's Attendance</div>
                </div>
            </div>

            <!-- User Management Section -->
            <div class="section">
                <h3>👥 Registered Users Management</h3>
                <p style="color: #666; margin-bottom: 20px;">Select a user to view details and remove their registration if needed</p>

                <?php if ($users_result->num_rows > 0): ?>
                    <!-- User Selection Dropdown -->
                    <div style="margin-bottom: 30px;">
                        <label for="user_select" style="display: block; margin-bottom: 10px; font-weight: 600; color: #333;">👤 Select User:</label>
                        <select id="user_select" style="width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 16px;" onchange="showUserDetails(this.value)">
                            <option value="">-- Choose a registered user --</option>
                            <?php
                            // Reset result pointer
                            $users_result->data_seek(0);
                            while ($user = $users_result->fetch_assoc()):
                            ?>
                                <option value="<?= $user['id'] ?>"
                                        data-name="<?= htmlspecialchars($user['full_name']) ?>"
                                        data-eid="<?= htmlspecialchars($user['eid_cid_permit']) ?>"
                                        data-uid="<?= htmlspecialchars($user['unique_id']) ?>"
                                        data-phone="<?= htmlspecialchars($user['phone_model']) ?>"
                                        data-fingerprint="<?= htmlspecialchars($user['device_fingerprint'] ? 'Enhanced Security' : 'Legacy') ?>"
                                        data-regdate="<?= $user['registration_timestamp'] ? date('M j, Y H:i', strtotime($user['registration_timestamp'])) : date('M j, Y', strtotime($user['created_at'])) ?>"
                                        data-date="<?= date('M j, Y', strtotime($user['created_at'])) ?>">
                                    <?= htmlspecialchars($user['full_name']) ?> (<?= htmlspecialchars($user['unique_id']) ?>) <?= $user['device_fingerprint'] ? '🔒' : '📱' ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <!-- User Details Display -->
                    <div id="user_details" style="display: none; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 25px; border-radius: 15px; border: 2px solid #dee2e6; margin-bottom: 20px;">
                        <h4 style="margin-top: 0; color: #333;">📋 User Details</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                            <div>
                                <strong>👤 Full Name:</strong><br>
                                <span id="detail_name" style="color: #333;"></span>
                            </div>
                            <div>
                                <strong>🆔 EID/CID/Permit:</strong><br>
                                <span id="detail_eid" style="color: #333;"></span>
                            </div>
                            <div>
                                <strong>🔐 Unique ID:</strong><br>
                                <span id="detail_uid" style="color: #1976d2; font-family: monospace; font-weight: 600;"></span>
                            </div>
                            <div>
                                <strong>📱 Phone Model:</strong><br>
                                <span id="detail_phone" style="color: #333; font-family: monospace; font-size: 0.9rem;"></span>
                            </div>
                            <div>
                                <strong>🔒 Security Level:</strong><br>
                                <span id="detail_fingerprint" style="color: #333; font-weight: 600;"></span>
                            </div>
                            <div>
                                <strong>📅 Registration Date:</strong><br>
                                <span id="detail_regdate" style="color: #333;"></span>
                            </div>
                        </div>

                        <!-- Remove User Form -->
                        <form method="POST" style="margin-top: 25px; text-align: center;" onsubmit="return confirmRemoval()">
                            <input type="hidden" name="action" value="remove_user">
                            <input type="hidden" name="user_id" id="remove_user_id" value="">
                            <button type="submit" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;">
                                🗑️ Remove User Registration
                            </button>
                        </form>
                    </div>

                    <script>
                    function showUserDetails(userId) {
                        const select = document.getElementById('user_select');
                        const details = document.getElementById('user_details');

                        if (userId === '') {
                            details.style.display = 'none';
                            return;
                        }

                        const option = select.querySelector(`option[value="${userId}"]`);
                        if (option) {
                            document.getElementById('detail_name').textContent = option.dataset.name;
                            document.getElementById('detail_eid').textContent = option.dataset.eid;
                            document.getElementById('detail_uid').textContent = option.dataset.uid;
                            document.getElementById('detail_phone').textContent = option.dataset.phone || 'N/A';
                            document.getElementById('detail_fingerprint').textContent = option.dataset.fingerprint;
                            document.getElementById('detail_regdate').textContent = option.dataset.regdate;
                            document.getElementById('remove_user_id').value = userId;

                            details.style.display = 'block';
                        }
                    }

                    function confirmRemoval() {
                        const userName = document.getElementById('detail_name').textContent;
                        const securityLevel = document.getElementById('detail_fingerprint').textContent;
                        return confirm(`Are you sure you want to remove ${userName}?\n\nThis will delete their device registration (${securityLevel}), unique ID, and all associated device data. This action cannot be undone.`);
                    }
                    </script>
                <?php else: ?>
                    <div style="text-align: center; color: #666; font-style: italic; padding: 40px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px;">
                        <h4>📝 No Users Registered Yet</h4>
                        <p>No users have registered their phones yet. Once users register, they will appear here for management.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Attendance Records Section -->
            <div class="section">
                <h3>📊 Attendance Records</h3>
                <div class="table-container">
                    <table id="attendance" class="display nowrap" style="width:100%">
                        <thead>
                            <tr>
                                <th data-export="Full Name">👤 Full Name</th>
                                <th data-export="EID/CID/Permit">🆔 EID/CID/Permit</th>
                                <th data-export="Unique ID">🔐 Unique ID</th>
                                <th data-export="Position">🏢 Position</th>
                                <th data-export="Division">🏢 Division</th>
                                <th data-export="Clock In">🕐 Clock In</th>
                                <th data-export="Clock Out">🕐 Clock Out</th>
                                <th data-export="Date">📅 Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $attendance_result->fetch_assoc()): ?>
                                <tr>
                                    <td data-export="<?= htmlspecialchars($row['full_name']) ?>"><strong><?= htmlspecialchars($row['full_name']) ?></strong></td>
                                    <td data-export="<?= htmlspecialchars($row['eid_cid_permit']) ?>"><?= htmlspecialchars($row['eid_cid_permit']) ?></td>
                                    <td data-export="<?= htmlspecialchars($row['unique_id'] ?? 'N/A') ?>"><span style="color:#1976d2;font-weight:600;font-family:monospace;"><?= htmlspecialchars($row['unique_id'] ?? 'N/A') ?></span></td>
                                    <td data-export="<?= htmlspecialchars($row['position_title']) ?>"><?= htmlspecialchars($row['position_title']) ?></td>
                                    <td data-export="<?= htmlspecialchars($row['division']) ?>"><?= htmlspecialchars($row['division']) ?></td>
                                    <td data-export="<?= $row['clock_in'] ? htmlspecialchars($row['clock_in']) : 'Not clocked in' ?>"><?= $row['clock_in'] ? "<span style='color:#28a745;font-weight:600;'>✅ " . htmlspecialchars($row['clock_in']) . "</span>" : "<span style='color:#dc3545;'>❌ Not clocked in</span>" ?></td>
                                    <td data-export="<?= $row['clock_out'] ? htmlspecialchars($row['clock_out']) : 'Still working' ?>"><?= $row['clock_out'] ? "<span style='color:#dc3545;font-weight:600;'>🔴 " . htmlspecialchars($row['clock_out']) . "</span>" : "<span style='color:#ffc107;'>⏳ Still working</span>" ?></td>
                                    <td data-export="<?= htmlspecialchars($row['date']) ?>"><?= htmlspecialchars($row['date']) ?></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>


        </div>
    </div>

    <!-- DataTables and Export Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#attendance').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    'print',
                    {
                        extend: 'excelHtml5',
                        title: 'Attendance_Report',
                        exportOptions: {
                            format: {
                                header: function (data, row, column, node) {
                                    // Use data-export attribute for headers, otherwise use plain text
                                    return $(node).attr('data-export') || $(node).text();
                                },
                                body: function (data, row, column, node) {
                                    // Use data-export attribute for body cells, otherwise use plain text
                                    return $(node).attr('data-export') || $(node).text();
                                }
                            }
                        }
                    }
                ],
                order: [] // Disable DataTables initial ordering to keep SQL order
            });
        });
    </script>
</body>
</html>
