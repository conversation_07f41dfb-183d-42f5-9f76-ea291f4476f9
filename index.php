<?php
session_start();
require_once('db.php');
require_once('network_security.php');
date_default_timezone_set('Asia/Thimphu');

// Check network access before proceeding
blockUnauthorizedAccess();

// Check if user is admin (based on session)
$is_admin = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$admin_name = $_SESSION['admin_name'] ?? '';

// Get phone model from POST or cookie for regular users
$phone_model = $_POST['phone_model'] ?? ($_COOKIE['phone_model'] ?? null);
$staff = null;
$today = date("Y-m-d");
$now = date("H:i:s");

// Check if regular user is registered (only if not admin)
if (!$is_admin && $phone_model) {
    $stmt = $conn->prepare("SELECT * FROM staff WHERE phone_model = ? AND unique_id IS NOT NULL AND unique_id != ''");
    $stmt->bind_param("s", $phone_model);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 1) {
        $staff = $result->fetch_assoc();
    }
}

// Handle attendance POST (clock_in / clock_out) for regular users
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['action']) && $staff && !$is_admin) {
    $action = $_POST['action'];

    // Check today's attendance record for this staff
    $stmt = $conn->prepare("SELECT * FROM attendance WHERE staff_id = ? AND date = ?");
    $stmt->bind_param("is", $staff['id'], $today);
    $stmt->execute();
    $res = $stmt->get_result();
    $row = $res->fetch_assoc();

    if ($action === "clock_in") {
        if ($row && $row['clock_in']) {
            $_SESSION['msg'] = "⏰ Already clocked in at " . $row['clock_in'];
        } elseif ($row) {
            $stmt = $conn->prepare("UPDATE attendance SET clock_in = ?, clock_in_phone_model = ? WHERE id = ?");
            $stmt->bind_param("ssi", $now, $phone_model, $row['id']);
            $stmt->execute();
            $_SESSION['msg'] = "✅ Clocked in at $now";
        } else {
            $stmt = $conn->prepare("INSERT INTO attendance (staff_id, date, clock_in, clock_in_phone_model) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("isss", $staff['id'], $today, $now, $phone_model);
            $stmt->execute();
            $_SESSION['msg'] = "✅ Clocked in at $now";
        }
    } elseif ($action === "clock_out") {
        if (!$row || !$row['clock_in']) {
            $_SESSION['msg'] = "⚠️ You haven't clocked in today.";
        } elseif ($row['clock_out']) {
            $_SESSION['msg'] = "⏰ Already clocked out at " . $row['clock_out'];
        } else {
            $stmt = $conn->prepare("UPDATE attendance SET clock_out = ?, clock_out_phone_model = ? WHERE id = ?");
            $stmt->bind_param("ssi", $now, $phone_model, $row['id']);
            $stmt->execute();
            $_SESSION['msg'] = "✅ Clocked out at $now";
        }
    }

    header("Location: " . $_SERVER['REQUEST_URI']);
    exit;
}

// Get system statistics
$total_staff = $conn->query("SELECT COUNT(*) as count FROM staff")->fetch_assoc()['count'];
$registered_phones = $conn->query("SELECT COUNT(*) as count FROM staff WHERE phone_model IS NOT NULL AND phone_model != ''")->fetch_assoc()['count'];
$today_attendance = $conn->query("SELECT COUNT(*) as count FROM attendance WHERE date = CURDATE()")->fetch_assoc()['count'];
?>

<!DOCTYPE html>
<html>
<head>
    <title>Attendance System - Home</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .nav-card {
            background-color: #fff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .nav-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .nav-card h3 {
            margin-top: 0;
            color: #333;
        }
        .nav-card p {
            color: #666;
            margin-bottom: 20px;
        }
        .nav-card a {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-card a:hover {
            background-color: #0056b3;
        }
        .setup-card a {
            background-color: #28a745;
        }
        .setup-card a:hover {
            background-color: #218838;
        }
        .register-card a {
            background-color: #17a2b8;
        }
        .register-card a:hover {
            background-color: #138496;
        }
        .qr-card a {
            background-color: #ffc107;
            color: #212529;
        }
        .qr-card a:hover {
            background-color: #e0a800;
        }
        .attendance-card a {
            background-color: #dc3545;
        }
        .attendance-card a:hover {
            background-color: #c82333;
        }
        .admin-card a {
            background-color: #6f42c1;
        }
        .admin-card a:hover {
            background-color: #5a32a3;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0056b3;
        }
        .flow-diagram {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .flow-step {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 20px;
            font-size: 14px;
        }
        .flow-arrow {
            font-size: 20px;
            color: #666;
            margin: 0 10px;
        }
        .container {
            position: relative;
        }
        .admin-login-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .admin-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        .instructions-btn {
            position: absolute;
            top: 20px;
            right: 180px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            z-index: 1000;
            cursor: pointer;
            border: none;
        }
        .instructions-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        /* Instructions Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        .close:hover {
            color: #000;
        }
        .instruction-section {
            margin: 25px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        .instruction-section h3 {
            color: #007bff;
            margin-top: 0;
        }
        .instruction-steps {
            list-style: none;
            padding: 0;
        }
        .instruction-steps li {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 3px solid #28a745;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .instruction-steps li strong {
            color: #28a745;
        }
        @media (max-width: 768px) {
            .admin-login-btn, .instructions-btn {
                position: static;
                display: inline-block;
                text-align: center;
                margin: 0 5px 20px 5px;
                max-width: 120px;
                font-size: 0.75rem;
                padding: 6px 12px;
            }
            .modal-content {
                margin: 10% auto;
                padding: 20px;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="instructions-btn" onclick="openInstructionsModal()">📖 Instructions</button>
        <?php if (!$is_admin): ?>
            <a href="admin_login.php" class="admin-login-btn">👨‍💼 Admin Login</a>
        <?php else: ?>
            <a href="admin_login.php?logout=1" class="admin-login-btn" style="background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);">🚪 Admin Logout</a>
        <?php endif; ?>
        <h1>📋 Attendance System Dashboard</h1>
        
        <?php if (isset($_SESSION['msg'])): ?>
            <div style="margin: 20px 0; padding: 15px; background: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 5px; text-align: center;">
                <?= htmlspecialchars($_SESSION['msg']) ?>
            </div>
            <?php unset($_SESSION['msg']); ?>
        <?php endif; ?>

        <?php if ($is_admin): ?>
            <div class="instructions">
                <h3>👨‍💼 Admin Dashboard - Welcome <?= htmlspecialchars($admin_name) ?>!</h3>
                <p><strong>Admin Features:</strong></p>
                <ul>
                    <li>Manage user registrations and remove users</li>
                    <li>Generate QR codes for registration and attendance</li>
                    <li>View comprehensive attendance reports</li>
                    <li>Monitor system statistics and user activity</li>
                </ul>
                <p><strong>Note:</strong> Admin access is restricted to authorized personnel only.</p>
            </div>
        <?php elseif ($staff): ?>
            <!-- Registered User - Show Clock In/Out Interface -->
            <div style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 2px solid #28a745; border-left: 6px solid #28a745; padding: 25px; border-radius: 15px; margin: 25px 0; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.1);">
                <h3 style="margin: 0 0 20px 0; color: #155724; text-align: center;">👤 Welcome, <?= htmlspecialchars($staff['full_name']) ?>!</h3>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px; border-left: 4px solid #28a745;">
                        <div style="font-size: 0.85rem; color: #666; margin-bottom: 5px; font-weight: 500;">👤 Full Name</div>
                        <div style="font-weight: 600; color: #333; font-size: 1rem;"><?= htmlspecialchars($staff['full_name']) ?></div>
                    </div>
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px; border-left: 4px solid #28a745;">
                        <div style="font-size: 0.85rem; color: #666; margin-bottom: 5px; font-weight: 500;">🆔 EID/CID/Permit</div>
                        <div style="font-weight: 600; color: #333; font-size: 1rem;"><?= htmlspecialchars($staff['eid_cid_permit']) ?></div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 15px; border-radius: 10px; border-left: 4px solid #2196f3; text-align: center;">
                        <div style="font-size: 0.85rem; color: #666; margin-bottom: 5px; font-weight: 500;">🔐 Unique ID</div>
                        <div style="color: #1976d2; font-size: 1.1rem; font-family: monospace; letter-spacing: 1px; font-weight: 600;"><?= htmlspecialchars($staff['unique_id']) ?></div>
                    </div>
                </div>

                <form method="POST" style="text-align: center; margin: 30px 0; padding: 30px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px;">
                    <input type="hidden" name="phone_model" value="<?= htmlspecialchars($phone_model ?? '') ?>">
                    <button type="submit" name="action" value="clock_in" style="font-size: 1.1rem; padding: 18px 35px; margin: 10px; border: none; border-radius: 25px; cursor: pointer; font-weight: 600; min-width: 160px; transition: all 0.3s ease; text-transform: uppercase; letter-spacing: 1px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                        🕐 Clock In
                    </button>
                    <button type="submit" name="action" value="clock_out" style="font-size: 1.1rem; padding: 18px 35px; margin: 10px; border: none; border-radius: 25px; cursor: pointer; font-weight: 600; min-width: 160px; transition: all 0.3s ease; text-transform: uppercase; letter-spacing: 1px; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;">
                        🕐 Clock Out
                    </button>
                </form>
            </div>
        <?php else: ?>
            <!-- Logged Out / Unregistered User - Show Basic Info Only -->
            <div class="instructions">
                <h3>📱 Staff Attendance System</h3>
                <p><strong>Welcome to the attendance system!</strong></p>
                <p>This system allows staff to clock in and out for daily work attendance.</p>
                <p><strong>For Staff:</strong> Scan the attendance QR code to access your personal attendance page.</p>
                <p><strong>For Admins:</strong> Use the admin login button above to access administrative features.</p>
            </div>
        <?php endif; ?>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?= $total_staff ?></div>
                <div class="stat-label">Total Staff</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $registered_phones ?></div>
                <div class="stat-label">Registered Phones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $today_attendance ?></div>
                <div class="stat-label">Today's Attendance</div>
            </div>
        </div>

        <?php if ($is_admin): ?>
            <div class="flow-diagram">
                <h3>👨‍💼 Admin Workflow</h3>
                <div>
                    <span class="flow-step">1. Generate QR Codes</span>
                    <span class="flow-arrow">→</span>
                    <span class="flow-step">2. Monitor Registrations</span>
                    <span class="flow-arrow">→</span>
                    <span class="flow-step">3. Manage Users</span>
                    <span class="flow-arrow">→</span>
                    <span class="flow-step">4. View Reports</span>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($is_admin): ?>
        <div class="nav-grid">
            <!-- Admin Navigation - Only Essential Features -->
            <div class="nav-card qr-card">
                <h3>🔗 QR Code Generator</h3>
                <p>Generate registration and attendance QR codes for the system</p>
                <a href="qr_generator.php">Generate QR Codes</a>
            </div>

            <div class="nav-card admin-card">
                <h3>👨‍💼 Admin Dashboard</h3>
                <p>User management, attendance reports, and system administration</p>
                <a href="admin_dashboard.php">Admin Dashboard</a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Today's Attendance Table for All Users -->
        <?php if ($is_admin || $staff || (!$is_admin && !$staff)): ?>
        <div style="margin-top: 40px; padding-top: 30px; border-top: 2px solid #e9ecef;">
            <h3 style="color: #333; margin-bottom: 20px; text-align: center;">📅 Today's Attendance - <?= date('F j, Y') ?></h3>
            <div style="overflow-x: auto; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <table style="width: 100%; border-collapse: collapse; background: white; min-width: 700px;">
                    <thead>
                        <tr>
                            <th style="padding: 15px 12px; text-align: left; border-bottom: 1px solid #e9ecef; font-size: 0.9rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); font-weight: 600; color: #495057;">👤 Full Name</th>
                            <th style="padding: 15px 12px; text-align: left; border-bottom: 1px solid #e9ecef; font-size: 0.9rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); font-weight: 600; color: #495057;">🆔 EID/CID/Permit</th>
                            <th style="padding: 15px 12px; text-align: left; border-bottom: 1px solid #e9ecef; font-size: 0.9rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); font-weight: 600; color: #495057;">🔐 Unique ID</th>
                            <th style="padding: 15px 12px; text-align: left; border-bottom: 1px solid #e9ecef; font-size: 0.9rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); font-weight: 600; color: #495057;">🏢 Position</th>
                            <th style="padding: 15px 12px; text-align: left; border-bottom: 1px solid #e9ecef; font-size: 0.9rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); font-weight: 600; color: #495057;">🕐 Clock In</th>
                            <th style="padding: 15px 12px; text-align: left; border-bottom: 1px solid #e9ecef; font-size: 0.9rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); font-weight: 600; color: #495057;">🕐 Clock Out</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $today = date("Y-m-d");
                        $result = $conn->query("
                            SELECT s.full_name, s.eid_cid_permit, s.unique_id, s.position_title, s.division,
                                   a.clock_in, a.clock_out
                            FROM attendance a
                            JOIN staff s ON a.staff_id = s.id
                            WHERE a.date = '$today'
                            ORDER BY a.clock_in DESC
                        ");

                        if ($result->num_rows > 0) {
                            while ($row = $result->fetch_assoc()) {
                                echo "<tr style='border-bottom: 1px solid #e9ecef;'>";
                                echo "<td style='padding: 15px 12px; font-size: 0.9rem;'><strong>" . htmlspecialchars($row['full_name']) . "</strong></td>";
                                echo "<td style='padding: 15px 12px; font-size: 0.9rem;'>" . htmlspecialchars($row['eid_cid_permit']) . "</td>";
                                echo "<td style='padding: 15px 12px; font-size: 0.9rem;'><span style='color:#1976d2;font-weight:600;font-family:monospace;'>" . htmlspecialchars($row['unique_id'] ?? 'N/A') . "</span></td>";
                                echo "<td style='padding: 15px 12px; font-size: 0.9rem;'>" . htmlspecialchars($row['position_title']) . "</td>";
                                echo "<td style='padding: 15px 12px; font-size: 0.9rem;'>" . ($row['clock_in'] ? "<span style='color:#28a745;font-weight:600;'>✅ " . htmlspecialchars($row['clock_in']) . "</span>" : "<span style='color:#dc3545;'>❌ Not clocked in</span>") . "</td>";
                                echo "<td style='padding: 15px 12px; font-size: 0.9rem;'>" . ($row['clock_out'] ? "<span style='color:#dc3545;font-weight:600;'>🔴 " . htmlspecialchars($row['clock_out']) . "</span>" : "<span style='color:#ffc107;'>⏳ Still working</span>") . "</td>";
                                echo "</tr>";
                            }
                        } else {
                            echo "<tr><td colspan='6' style='text-align:center;color:#666;font-style:italic;padding:30px;'>No attendance records for today</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>



        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>Attendance System v2.0 - Enhanced with Device Security</p>
            <p>Current Time: <?= date('Y-m-d H:i:s') ?></p>
        </div>
    </div>

    <!-- Instructions Modal -->
    <div id="instructionsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeInstructionsModal()">&times;</span>
            <h2 style="color: #007bff; margin-bottom: 30px; text-align: center;">📖 Attendance System Instructions</h2>

            <div class="instruction-section">
                <h3>📱 For Staff Members - Device Registration</h3>
                <ol class="instruction-steps">
                    <li><strong>Step 1:</strong> Scan the "Registration QR Code" with your mobile device</li>
                    <li><strong>Step 2:</strong> Enter your EID/CID/Permit number when prompted</li>
                    <li><strong>Step 3:</strong> Your device will be registered with enhanced security features</li>
                    <li><strong>Step 4:</strong> You will receive a unique ID for your attendance tracking</li>
                    <li><strong>Note:</strong> Each device can only be registered to one staff member for security</li>
                </ol>
            </div>

            <div class="instruction-section">
                <h3>🕐 For Staff Members - Daily Attendance</h3>
                <ol class="instruction-steps">
                    <li><strong>Clock In:</strong> Scan the "Attendance QR Code" and tap "Clock In" when you arrive at work</li>
                    <li><strong>Clock Out:</strong> Scan the "Attendance QR Code" and tap "Clock Out" when you finish work</li>
                    <li><strong>View Status:</strong> Your current attendance status will be displayed on the attendance page</li>
                    <li><strong>Security:</strong> Only your registered device can be used for attendance tracking</li>
                </ol>
            </div>

            <div class="instruction-section">
                <h3>👨‍💼 For Administrators</h3>
                <ol class="instruction-steps">
                    <li><strong>Generate QR Codes:</strong> Use the QR Generator to create registration and attendance codes</li>
                    <li><strong>Monitor Registrations:</strong> View all staff registrations in the Admin Dashboard</li>
                    <li><strong>View Reports:</strong> Access daily, weekly, and monthly attendance reports</li>
                    <li><strong>Manage Users:</strong> Add, edit, or remove staff members from the system</li>
                    <li><strong>System Security:</strong> Enhanced device fingerprinting prevents unauthorized access</li>
                </ol>
            </div>

            <div class="instruction-section">
                <h3>🔒 Security Features</h3>
                <ol class="instruction-steps">
                    <li><strong>Device Fingerprinting:</strong> Each device is uniquely identified using multiple characteristics</li>
                    <li><strong>One Device Per User:</strong> Prevents multiple users from sharing the same device</li>
                    <li><strong>Secure Tracking:</strong> All attendance actions are logged with device verification</li>
                    <li><strong>Backward Compatibility:</strong> Supports both new and legacy registrations</li>
                </ol>
            </div>

            <div style="text-align: center; margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 10px;">
                <p style="margin: 0; color: #1976d2; font-weight: 600;">Need Help?</p>
                <p style="margin: 5px 0 0 0; color: #1976d2;">Contact your system administrator for assistance</p>
            </div>
        </div>
    </div>

    <script>
        function openInstructionsModal() {
            document.getElementById('instructionsModal').style.display = 'block';
        }

        function closeInstructionsModal() {
            document.getElementById('instructionsModal').style.display = 'none';
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('instructionsModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>

</body>
</html>
